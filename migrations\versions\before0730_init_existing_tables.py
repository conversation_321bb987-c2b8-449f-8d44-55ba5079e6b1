"""init existing tables

Revision ID: before0730
Revises: 
Create Date: 2025-07-30 10:34:17.760189

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision = 'before0730'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('dataset',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('publish_date', sa.String(length=50), nullable=True),
    sa.Column('source', sa.String(length=100), nullable=True),
    sa.Column('download_url', sa.String(length=255), nullable=True),
    sa.Column('dataset_info', mysql.LONGTEXT(), nullable=True),
    sa.Column('dataset_type', sa.String(length=50), server_default='系统', nullable=False),
    sa.Column('visibility', sa.String(length=50), server_default='公开', nullable=False),
    sa.Column('format', sa.String(length=50), server_default='QA', nullable=False),
    sa.Column('jinja2_template', mysql.LONGTEXT(), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='1', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=64), nullable=False),
    sa.Column('password_hash', sa.String(length=256), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_username'), ['username'], unique=True)

    op.create_table('chat_session',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_name', sa.String(length=150), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('config_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dataset_category',
    sa.Column('dataset_id', sa.Integer(), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['category.id'], ),
    sa.ForeignKeyConstraint(['dataset_id'], ['dataset.id'], ),
    sa.PrimaryKeyConstraint('dataset_id', 'category_id')
    )
    op.create_table('model',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('model_type', sa.String(length=50), nullable=False),
    sa.Column('api_base_url', sa.String(length=255), nullable=False),
    sa.Column('model_identifier', sa.String(length=100), nullable=False),
    sa.Column('encrypted_api_key', sa.String(length=512), nullable=True),
    sa.Column('provider_name', sa.String(length=100), nullable=True),
    sa.Column('is_system_model', sa.Boolean(), nullable=False),
    sa.Column('system_prompt', sa.Text(), nullable=True),
    sa.Column('default_temperature', sa.Float(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('is_validated', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('model_efficiency',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('model_name', sa.String(length=150), nullable=False),
    sa.Column('dataset_name', sa.String(length=150), nullable=False),
    sa.Column('concurrency', sa.Integer(), nullable=False),
    sa.Column('num_requests', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('summary_results', sa.Text(), nullable=True),
    sa.Column('percentile_results', sa.Text(), nullable=True),
    sa.Column('raw_output', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('chat_message',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.Integer(), nullable=False),
    sa.Column('model_id', sa.Integer(), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('settings_snapshot', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['model_id'], ['model.id'], ),
    sa.ForeignKeyConstraint(['session_id'], ['chat_session.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('evaluation_effectiveness',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('model_id', sa.Integer(), nullable=False),
    sa.Column('judge_model_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=150), nullable=True),
    sa.Column('temperature', sa.Float(), nullable=False),
    sa.Column('max_tokens', sa.Integer(), nullable=False),
    sa.Column('top_k', sa.Integer(), nullable=True),
    sa.Column('top_p', sa.Float(), nullable=True),
    sa.Column('judge_worker_num', sa.Integer(), nullable=True),
    sa.Column('eval_batch_size', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('result_summary', sa.JSON(), nullable=True),
    sa.Column('limit', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['judge_model_id'], ['model.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['model.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('evaluation_effectiveness_dataset',
    sa.Column('evaluation_id', sa.Integer(), nullable=False),
    sa.Column('dataset_id', sa.Integer(), nullable=False),
    sa.Column('subset', sa.String(length=100), nullable=True),
    sa.Column('split', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['dataset_id'], ['dataset.id'], ),
    sa.ForeignKeyConstraint(['evaluation_id'], ['evaluation_effectiveness.id'], ),
    sa.PrimaryKeyConstraint('evaluation_id', 'dataset_id')
    )
    op.create_table('evaluation_effectiveness_result',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('evaluation_id', sa.Integer(), nullable=False),
    sa.Column('dataset_id', sa.Integer(), nullable=False),
    sa.Column('question', sa.Text(), nullable=False),
    sa.Column('reference_answer', sa.Text(), nullable=True),
    sa.Column('model_answer', sa.Text(), nullable=False),
    sa.Column('score', sa.Float(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['dataset_id'], ['dataset.id'], ),
    sa.ForeignKeyConstraint(['evaluation_id'], ['evaluation_effectiveness.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('evaluation_effectiveness_result')
    op.drop_table('evaluation_effectiveness_dataset')
    op.drop_table('evaluation_effectiveness')
    op.drop_table('chat_message')
    op.drop_table('model_efficiency')
    op.drop_table('model')
    op.drop_table('dataset_category')
    op.drop_table('chat_session')
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_username'))

    op.drop_table('user')
    op.drop_table('dataset')
    op.drop_table('category')
    # ### end Alembic commands ###
