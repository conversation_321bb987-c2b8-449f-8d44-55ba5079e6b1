"""add new tables

Revision ID: 95c1fe3b7e18
Revises: before0730
Create Date: 2025-07-30 10:37:25.845121

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '95c1fe3b7e18'
down_revision = 'before0730'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rag_evaluation',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=150), nullable=True),
    sa.Column('judge_model_id', sa.Integer(), nullable=False),
    sa.Column('judge_temperature', sa.Float(), nullable=False),
    sa.Column('judge_max_tokens', sa.Integer(), nullable=False),
    sa.Column('judge_top_k', sa.Integer(), nullable=True),
    sa.Column('judge_top_p', sa.Float(), nullable=True),
    sa.Column('embedding_model_id', sa.Integer(), nullable=False),
    sa.Column('embedding_dimension', sa.Integer(), nullable=False),
    sa.Column('evaluation_metrics', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('result_summary', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['embedding_model_id'], ['model.id'], ),
    sa.ForeignKeyConstraint(['judge_model_id'], ['model.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rag_evaluation_dataset',
    sa.Column('evaluation_id', sa.Integer(), nullable=False),
    sa.Column('dataset_id', sa.Integer(), nullable=False),
    sa.Column('subset', sa.String(length=100), nullable=True),
    sa.Column('split', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['dataset_id'], ['dataset.id'], ),
    sa.ForeignKeyConstraint(['evaluation_id'], ['rag_evaluation.id'], ),
    sa.PrimaryKeyConstraint('evaluation_id', 'dataset_id')
    )
    op.create_table('rag_evaluation_result',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('evaluation_id', sa.Integer(), nullable=False),
    sa.Column('dataset_id', sa.Integer(), nullable=False),
    sa.Column('user_input', sa.Text(), nullable=False),
    sa.Column('retrieved_contexts', sa.JSON(), nullable=True),
    sa.Column('reference_answer', sa.Text(), nullable=True),
    sa.Column('response', sa.Text(), nullable=True),
    sa.Column('relevance_score', sa.Float(), nullable=True),
    sa.Column('faithfulness_score', sa.Float(), nullable=True),
    sa.Column('answer_correctness_score', sa.Float(), nullable=True),
    sa.Column('context_precision_score', sa.Float(), nullable=True),
    sa.Column('context_recall_score', sa.Float(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['dataset_id'], ['dataset.id'], ),
    sa.ForeignKeyConstraint(['evaluation_id'], ['rag_evaluation.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rag_evaluation_result')
    op.drop_table('rag_evaluation_dataset')
    op.drop_table('rag_evaluation')
    # ### end Alembic commands ###
