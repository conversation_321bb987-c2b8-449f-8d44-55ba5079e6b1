# LLM-EVA Docker 环境变量配置文件
# 复制此文件为 .env 并根据实际情况修改配置
MYSQL_DATABASE=llm_eva
MYSQL_USER=llm_user
MYSQL_PASSWORD=llm_password
MYSQL_CHARACTER_SET_SERVER=utf8mb4
MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci

DB_HOST=mysql
DB_PORT=3306

FLASK_APP=run.py
FLASK_ENV=production
SECRET_KEY=3d6f45a5f7b8c9e1d2a0b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7

SYSTEM_PROVIDER_BASE_URL=https://openrouter.ai/api/v1
SYSTEM_PROVIDER_API_KEY=ST-xxx

WTF_CSRF_ENABLED=True

WEB_PORT=9999

DATA_UPLOADS_DIR=./data/uploads
DATA_OUTPUTS_DIR=./data/outputs
DATA_LOGS_DIR=./data/logs