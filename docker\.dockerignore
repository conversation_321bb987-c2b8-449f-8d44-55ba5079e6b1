# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.mypy_cache
.pytest_cache
.hypothesis

# 虚拟环境
.venv
venv/
ENV/
env/
.env

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~
.cursor/

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件（运行时生成）
instance/
migrations/
outputs/
uploads/
logs/
*.db
*.sqlite

# 测试文件
test_*.py
*_test.py

# 但保留初始化脚本
!init_database.py

# Docker相关（避免递归复制）
Dockerfile*
docker-compose*.yml
.dockerignore

# 文档和说明（减少镜像大小）
README.md
需求.md
*.md
!DOCKER_DEPLOYMENT.md
*.jpg
*.png
*.gif

# 开发工具配置
.editorconfig
.flake8
.pylintrc
pyproject.toml 