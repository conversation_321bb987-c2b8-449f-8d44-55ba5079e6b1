{% extends "base.html" %}

{% block title %}RAG评估历史 - {{ super() }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold">RAG评估历史</h1>
            <p class="text-base-content/70 mt-2">查看和管理您的RAG评估任务</p>
        </div>
        <a href="{{ url_for('rag_eval.create') }}" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>新建RAG评估
        </a>
    </div>

    {% if evaluations.items %}
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body p-0">
            <div class="overflow-x-auto">
                <table class="table table-zebra">
                    <thead>
                        <tr>
                            <th>评估名称</th>
                            <th>数据集</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for evaluation in evaluations.items %}
                        <tr>
                            <td>
                                <div class="font-semibold">{{ evaluation.name }}</div>
                            </td>

                            <td>
                                <div class="text-sm">
                                    {% for dataset_rel in evaluation.datasets %}
                                    <div class="badge badge-outline badge-sm mb-1">{{ dataset_rel.dataset.name }}</div>
                                    {% endfor %}
                                </div>
                            </td>
                            <td>
                                {% if evaluation.status == 'pending' %}
                                <div class="badge badge-warning">
                                    <i class="fas fa-clock mr-1"></i>等待中
                                </div>
                                {% elif evaluation.status == 'running' %}
                                <div class="badge badge-info">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>运行中
                                </div>
                                {% elif evaluation.status == 'completed' %}
                                <div class="badge badge-success">
                                    <i class="fas fa-check mr-1"></i>已完成
                                </div>
                                {% elif evaluation.status == 'failed' %}
                                <div class="badge badge-error">
                                    <i class="fas fa-times mr-1"></i>失败
                                </div>
                                {% else %}
                                <div class="badge badge-ghost">{{ evaluation.status }}</div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-sm">{{ evaluation.created_at.strftime('%Y-%m-%d') }}</div>
                                <div class="text-xs text-base-content/70">{{ evaluation.created_at.strftime('%H:%M:%S')
                                    }}</div>
                            </td>
                            <td>
                                <div class="flex gap-2">
                                    <a href="{{ url_for('rag_eval.detail', evaluation_id=evaluation.id) }}"
                                        class="btn btn-sm btn-outline">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form method="POST"
                                        action="{{ url_for('rag_eval.delete', evaluation_id=evaluation.id) }}"
                                        class="inline" onsubmit="return confirm('确定要删除这个RAG评估吗？')">
                                        <button type="submit" class="btn btn-sm btn-outline btn-error">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if evaluations.pages > 1 %}
    <div class="flex justify-center mt-6">
        <div class="join">
            {% if evaluations.has_prev %}
            <a href="{{ url_for('rag_eval.history', page=evaluations.prev_num) }}" class="join-item btn">«</a>
            {% endif %}

            {% for page_num in evaluations.iter_pages() %}
            {% if page_num %}
            {% if page_num != evaluations.page %}
            <a href="{{ url_for('rag_eval.history', page=page_num) }}" class="join-item btn">{{ page_num }}</a>
            {% else %}
            <span class="join-item btn btn-active">{{ page_num }}</span>
            {% endif %}
            {% else %}
            <span class="join-item btn btn-disabled">…</span>
            {% endif %}
            {% endfor %}

            {% if evaluations.has_next %}
            <a href="{{ url_for('rag_eval.history', page=evaluations.next_num) }}" class="join-item btn">»</a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- 空状态 -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body text-center py-16">
            <i class="fas fa-search text-6xl text-base-content/30 mb-4"></i>
            <h2 class="text-2xl font-bold mb-2">还没有RAG评估记录</h2>
            <p class="text-base-content/70 mb-6">开始您的第一个RAG评估任务，评估检索增强生成系统的性能。</p>
            <a href="{{ url_for('rag_eval.create') }}" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>创建RAG评估
            </a>
        </div>
    </div>
    {% endif %}
</div>

<script>
    // 定期检查运行中的评估状态
    function checkRunningEvaluations() {
        const runningBadges = document.querySelectorAll('.badge-warning, .badge-info');
        
        if (runningBadges.length === 0) return;
                
        runningBadges.forEach(badge => {
            const row = badge.closest('tr');
            if (row) {
                const detailLink = row.querySelector('a[href*="/rag-evaluation/"]');
                if (detailLink) {
                    const evaluationId = detailLink.href.split('/').pop();

                    fetch(`/rag-evaluation/${evaluationId}/status`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.status !== badge.dataset.status) {
                                // 替换当前状态标签
                                let newBadge;
                                if (data.status === 'pending') {
                                    newBadge = `<div class="badge badge-warning" data-status="pending">
                                                <i class="fas fa-clock mr-1"></i>等待中</div>`;
                                } else if (data.status === 'running') {
                                    newBadge = `<div class="badge badge-info" data-status="running">
                                                <i class="fas fa-spinner fa-spin mr-1"></i>运行中</div>`;
                                } else if (data.status === 'completed') {
                                    newBadge = `<div class="badge badge-success" data-status="completed">
                                                <i class="fas fa-check mr-1"></i>已完成</div>`;
                                } else if (data.status === 'failed') {
                                    newBadge = `<div class="badge badge-error" data-status="failed">
                                                <i class="fas fa-times mr-1"></i>失败</div>`;
                                }
                                
                                if (newBadge) {
                                    const statusCell = badge.closest('td');
                                    statusCell.innerHTML = newBadge;
                                }
                            }
                        })
                        .catch(error => console.error('检查状态失败:', error));
                }
            }
        });
    }

    // 添加状态属性到徽章元素
    document.querySelectorAll('.badge-warning').forEach(badge => {
        badge.dataset.status = 'pending';
    });
    
    document.querySelectorAll('.badge-info').forEach(badge => {
        badge.dataset.status = 'running';
    });
    
    // 如果有运行中的评估，每10秒检查一次状态
    const runningEvaluations = document.querySelectorAll('.badge-warning, .badge-info');
    if (runningEvaluations.length > 0) {
        // 页面加载后立即执行一次检查
        setTimeout(checkRunningEvaluations, 1000);
        
        // 然后每10秒检查一次
        setInterval(checkRunningEvaluations, 10000); // 10秒检查一次
    }
</script>
{% endblock %}