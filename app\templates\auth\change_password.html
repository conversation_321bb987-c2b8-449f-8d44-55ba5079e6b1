{% extends "base.html" %}
{% import "_form_helpers.html" as forms %}

{% block title %}修改密码 - {{ super() }}{% endblock %}

{% block content %}
<div class="hero min-h-[calc(100vh-200px)] bg-base-100">
    <div class="hero-content flex-col">
        <div class="text-center">
            <h1 class="text-5xl font-bold">修改您的密码</h1>
            <p class="py-6 max-w-md">为了您的账户安全，请设置一个高强度且易于记忆的新密码。我们建议密码包含大小写字母、数字和特殊符号。</p>
        </div>
        <div class="card shrink-0 w-full max-w-lg shadow-2xl bg-base-200">
            <form method="POST" action="{{ url_for('auth.change_password') }}" novalidate class="card-body">
                {{ form.hidden_tag() }}
                {{ forms.render_field(form.current_password, type='password', placeholder='请输入当前使用的密码', required=True) }}
                {{ forms.render_field(form.new_password, type='password', placeholder='请输入您的新密码', required=True) }}
                {{ forms.render_field(form.confirm_new_password, type='password', placeholder='请再次确认您的新密码', required=True) }}
                <div class="form-control mt-6">
                    {{ forms.render_submit_button(form.submit, class='btn-primary', text='确认修改密码') }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 