{% extends "base.html" %}

{% block title %}仪表盘 - {{ super() }}{% endblock %}

{% block head_extra %}
<style>
    /* 为dashboard页面的btn-sm按钮设置自定义左右内边距 */
    .btn.btn-sm {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 页面标题和描述 -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
            <i class="fas fa-tachometer-alt mr-3 text-primary"></i>
            大模型评估仪表盘
        </h1>
        <p class="text-lg text-base-content/70">
            全面的大语言模型评估平台，提供模型/测试集管理、性能评估、效果评估、安全评估、RAG评估等功能
        </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <!-- 模型管理卡片 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure class="px-10 pt-10">
                <i class="fas fa-cogs text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-center">
                <h2 class="card-title">模型管理</h2>
                <div class="card-actions">
                    <a href="{{ url_for('models.list_models') }}" class="btn btn-primary btn-sm"><i class="fas fa-cog mr-1"></i> 前往管理</a>
                    {# Placeholder: {{ url_for('models_bp.list_models') }} #}
                </div>
            </div>
        </div>

        <!-- 开始对话卡片 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure class="px-10 pt-10">
                <i class="fas fa-comments text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-center">
                <h2 class="card-title">开始对话</h2>
                <div class="card-actions">
                    <a href="{{ url_for('chat.new_chat_session') }}" class="btn btn-primary btn-sm"><i class="fas fa-comments mr-1"></i> 进入对话</a>
                    <a href="{{ url_for('chat.chat_history_list') }}" class="btn btn-primary btn-sm"><i class="fas fa-eye mr-1"></i> 查看历史</a>
                </div>
            </div>
        </div>

        <!-- 测试集管理 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
             <figure class="px-10 pt-10">
                <i class="fas fa-vials text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-center">
                <h2 class="card-title">测试集管理</h2>
                <div class="card-actions">
                     <a href="{{ url_for('datasets.datasets_list') }}" class="btn btn-primary btn-sm"><i class="fas fa-cog mr-1"></i> 测试集管理</a>
                </div>
            </div>
        </div>

        <!-- 模型评测 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure class="px-10 pt-10">
                <i class="fas fa-chart-line text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-center">
                <h2 class="card-title">模型效果评估</h2>
                <div class="card-actions">
                    <a href="{{ url_for('evaluations.create_evaluation') }}" class="btn btn-primary btn-sm"><i class="fas fa-play mr-1"></i> 效果评估</a>
                    <a href="{{ url_for('evaluations.evaluations_list') }}" class="btn btn-primary btn-sm"><i class="fas fa-eye mr-1"></i>
                        查看历史</a>
                </div>
            </div>
        </div>

        <!-- 性能评估卡片 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure class="px-10 pt-10">
                <i class="fas fa-rocket text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-center">
                <h2 class="card-title">性能评估</h2>
                <div class="card-actions">
                    <a href="{{ url_for('perf_eval.create') }}" class="btn btn-primary btn-sm" ><i class="fas fa-play mr-1"></i> 性能评估</a>
                    <a href="{{ url_for('perf_eval.history') }}" class="btn btn-primary btn-sm"><i class="fas fa-eye mr-1"></i> 查看历史</a>
                </div>
            </div>
        </div>

        <!-- RAG评估卡片 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure class="px-10 pt-10">
                <i class="fas fa-search text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-center">
                <h2 class="card-title">RAG评估</h2>
                <div class="card-actions">
                    <a href="{{ url_for('rag_eval.create') }}" class="btn btn-primary btn-sm"><i class="fas fa-play mr-1"></i> RAG评估</a>
                    <a href="{{ url_for('rag_eval.history') }}" class="btn btn-primary btn-sm"><i class="fas fa-eye mr-1"></i> 查看历史</a>
                </div>
            </div>
        </div>

        <!-- 即将到来功能卡片 -->
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow border-2 border-dashed border-base-300">
            <figure class="px-10 pt-10">
                <i class="fas fa-clock text-5xl text-primary"></i>
            </figure>
            <div class="card-body items-center text-primary">
                <h2 class="card-title flex items-center gap-2">
                    即将到来
                </h2>
                
                <!-- 功能预告 -->
                <div class="space-y-2 mb-4">
                    <div class="flex items-center gap-2 text-sm">
                        <i class="fas fa-shield-alt text-error"></i>
                        <span class="font-semibold">安全评估</span>
                        <div class="badge badge-error badge-xs">开发中</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
{% endblock %} 