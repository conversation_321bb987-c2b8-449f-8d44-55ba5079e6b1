{% extends "base.html" %}

{% block title %}详细评估结果 - {{ evaluation.name }} - {{ super() }}{% endblock %}

{% block content %}
<style>
.formatted-text {
    white-space: pre-line;
    word-break: break-words;
    font-size: 0.875rem;
    line-height: 1.625;
}
.formatted-text:first-line {
    margin-top: 0;
    padding-top: 0;
}
/* 移除pre-line产生的首行上边距 */
.formatted-text::before {
    content: '';
    display: block;
    margin-top: -1.5em;
}
</style>
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">详细评估结果: {{ evaluation.name }}</h1>
        <div class="flex gap-2">
            <!-- Excel导出按钮 -->
            <button class="btn btn-success btn-sm" 
                    title="导出当前筛选结果为Excel文件"
                    onclick="startDownload()">
                <i class="fas fa-download mr-1"></i> 导出Excel
            </button>
            <a href="{{ url_for('evaluations.view_evaluation', evaluation_id=evaluation.id) }}" class="btn btn-outline btn-sm">
                <i class="fas fa-arrow-left mr-1"></i> 返回评估详情
            </a>
        </div>
    </div>

    <!-- 筛选状态提示 -->
    {% if search_query or min_score is not none or max_score is not none %}
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle mr-2"></i>
        <div>
            <span class="font-semibold">当前筛选条件：</span>
            {% if search_query %}
            <span class="badge badge-primary ml-1">搜索: "{{ search_query }}"</span>
            {% endif %}
            {% if min_score is not none %}
            <span class="badge badge-secondary ml-1">最低分: {{ min_score }}</span>
            {% endif %}
            {% if max_score is not none %}
            <span class="badge badge-secondary ml-1">最高分: {{ max_score }}</span>
            {% endif %}
            <br>
            <span class="text-sm">导出Excel将包含当前筛选条件下的 {{ total_results }} 条结果</span>
        </div>
    </div>
    {% endif %}

    <!-- 搜索和筛选表单 -->
    <form method="GET" action="{{ url_for('evaluations.view_detailed_results', evaluation_id=evaluation.id) }}" class="mb-6" id="filterForm">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- 搜索框 -->
            <div class="join w-full">
                <input type="text" name="search_query" placeholder="按问题内容搜索..." value="{{ search_query or '''' }}" class="input input-bordered join-item w-full"/>
                <button type="submit" class="btn btn-primary join-item">
                    <i class="fas fa-search mr-1"></i> 搜索
                </button>
            </div>
            
            <!-- 清除按钮 -->
            <div class="flex justify-end">
                {% if search_query or min_score is not none or max_score is not none %}
                <a href="{{ url_for('evaluations.view_detailed_results', evaluation_id=evaluation.id) }}" class="btn btn-ghost" title="清除所有筛选">
                    <i class="fas fa-times mr-1"></i> 清除筛选
                </a>
                {% endif %}
            </div>
        </div>
        
        <!-- 分数范围筛选 -->
        <div class="card bg-base-200 p-4">
            <h3 class="font-semibold mb-3">
                <i class="fas fa-filter mr-2"></i>按分数范围筛选
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label class="label">
                        <span class="label-text">最低分数</span>
                    </label>
                    <input type="number" name="min_score" step="0.01" min="0" max="1" 
                           value="{{ min_score if min_score is not none else '' }}" 
                           placeholder="0.0" class="input input-bordered w-full" id="minScore"/>
                </div>
                <div>
                    <label class="label">
                        <span class="label-text">最高分数</span>
                    </label>
                    <input type="number" name="max_score" step="0.01" min="0" max="1" 
                           value="{{ max_score if max_score is not none else '' }}" 
                           placeholder="1.0" class="input input-bordered w-full" id="maxScore"/>
                </div>
                <div>
                    <button type="submit" class="btn btn-secondary w-full">
                        <i class="fas fa-filter mr-1"></i> 应用筛选
                    </button>
                </div>
            </div>
        </div>
    </form>

    {% if results %}
        <div class="space-y-6">
            {% for result in results %}
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <div class="flex justify-between items-start">
                        <h3 class="card-title">问题 #{{ loop.index + (page-1) * per_page }}</h3>
                        {% if result.score is not none %}
                        <div class="badge badge-lg {% if result.score >= 8 %}badge-success{% elif result.score >= 5 %}badge-info{% else %}badge-warning{% endif %}">
                            {{ "%.1f"|format(result.score) }}
                        </div>
                        {% else %}
                        <div class="badge badge-lg badge-neutral">无评分</div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <div class="flex items-center gap-2 mb-2">
                            <i class="fas fa-question-circle text-primary"></i>
                            <span class="font-medium text-sm">问题</span>
                        </div>
                        <div class="p-3 bg-base-300 rounded-lg mt-1">
                            <!-- 直接显示后端格式化的user_prompt -->
                            <div class="formatted-text">
                                {% if result.user_prompt %}
                                    {{- result.user_prompt | trim -}}
                                {% else %}
                                    {{- result.question | trim -}}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if result.reference_answer %}
                    <div class="my-2">
                        <p class="font-semibold">参考答案:</p>
                        <div class="p-3 bg-base-300 rounded-lg mt-1 whitespace-pre-wrap break-words">{{ result.reference_answer | trim }}</div>
                    </div>
                    {% endif %}

                    <div class="my-2">
                        <p class="font-semibold">模型回答:</p>
                        <div class="p-3 bg-base-300 rounded-lg mt-1 whitespace-pre-wrap break-words">{{ result.model_answer | clean_json }}</div>
                    </div>

                    <!-- {% if result.feedback %}
                    <div class="my-2">
                        <p class="font-semibold">裁判评价:</p>
                        <div class="p-3 bg-base-300 rounded-lg mt-1 whitespace-pre-wrap">{{ result.feedback }}</div>
                    </div>
                    {% endif %} -->
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if total_pages > 1 %}
        <div class="flex justify-center mt-8">
            <div class="btn-group">
                {% set base_url_params = {'evaluation_id': evaluation.id} %}
                {% if search_query %}{% set _ = base_url_params.update({'search_query': search_query}) %}{% endif %}
                {% if min_score is not none %}{% set _ = base_url_params.update({'min_score': min_score}) %}{% endif %}
                {% if max_score is not none %}{% set _ = base_url_params.update({'max_score': max_score}) %}{% endif %}

                {% if page > 1 %}
                <a href="{{ url_for('evaluations.view_detailed_results', page=page-1, **base_url_params) }}" class="btn btn-outline">
                    <i class="fas fa-chevron-left"></i>
                </a>
                {% else %}
                <button class="btn btn-outline" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                {% endif %}

                {% for i in range(1, total_pages + 1) %}
                    {% if i == page %}
                    <a href="{{ url_for('evaluations.view_detailed_results', page=i, **base_url_params) }}" class="btn btn-active">{{ i }}</a>
                    {% elif i >= page - 2 and i <= page + 2 %}
                    <a href="{{ url_for('evaluations.view_detailed_results', page=i, **base_url_params) }}" class="btn btn-outline">{{ i }}</a>
                    {% elif i == page - 3 or i == page + 3 %}
                     <span class="btn btn-disabled">...</span>
                    {% endif %}
                {% endfor %}

                {% if page < total_pages %}
                <a href="{{ url_for('evaluations.view_detailed_results', page=page+1, **base_url_params) }}" class="btn btn-outline">
                    <i class="fas fa-chevron-right"></i>
                </a>
                {% else %}
                <button class="btn btn-outline" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                {% endif %}
            </div>
        </div>
        {% endif %}

    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle mr-2"></i>
            <span>没有找到符合条件的详细评估结果。{% if search_query %}请尝试修改搜索词。{% endif %}</span>
        </div>
    {% endif %}
</div>

<!-- 下载进度模态框 -->
<input type="checkbox" id="download-modal" class="modal-toggle" />
<div class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">
            <i class="fas fa-download mr-2"></i>正在准备Excel文件
        </h3>
        <div class="py-4">
            <p class="mb-4">正在导出评估结果，请稍候...</p>
            <progress class="progress progress-primary w-full"></progress>
            <p class="text-sm text-gray-500 mt-2">
                正在准备和下载Excel文件，请不要关闭页面。<br>
                下载完成后将自动关闭此提示。
            </p>
        </div>
        <div class="modal-action">
            <label for="download-modal" class="btn btn-outline">
                取消下载
            </label>
        </div>
    </div>
</div>

<script>
function startDownload() {
    // 构建下载URL
    var baseUrl = "{{ url_for('evaluations.export_to_excel', evaluation_id=evaluation.id) }}";
    var params = new URLSearchParams();
    
    {% if search_query %}
    params.append('search_query', '{{ search_query }}');
    {% endif %}
    {% if min_score is not none %}
    params.append('min_score', '{{ min_score }}');
    {% endif %}
    {% if max_score is not none %}
    params.append('max_score', '{{ max_score }}');
    {% endif %}
    
    var downloadUrl = baseUrl + (params.toString() ? '?' + params.toString() : '');
    
    // 创建临时链接并点击下载
    var link = document.createElement('a');
    link.href = downloadUrl;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

{% endblock %} 